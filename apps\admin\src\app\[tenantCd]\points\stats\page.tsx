import {
  readEarnPointCount,
  readEarnPointCountDaily,
  readEarnPointCountMonthly,
  readTransferPointCount,
} from "@workspace/db/crud/common/points";
import { Card } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { format, subYears } from "date-fns";
import { Suspense } from "react";
import { AppContent } from "../../components/container";
import { TenantPageProps } from "../../layout";
import { PointsStatsOverview } from "./components/overview";
import { PeriodPicker } from "./components/period-picker";
import { ChartRadialShape } from "./components/radial-chart";
import {
  UserStatsDailyChart,
  UserStatsMonthlyChart,
} from "./components/stats-chart";

const PointsStatsChartSkeleton = () => {
  return (
    <Card className="p-6">
      <Skeleton className="mb-4 h-6 w-48" />
      <Skeleton className="h-64 w-full" />
    </Card>
  );
};

const PointsStatsMonthly = async ({
  tenantCd,
  dateRange,
}: {
  tenantCd: string;
  dateRange: [Date, Date];
}) => {
  const result = await readEarnPointCountMonthly(tenantCd, dateRange);

  return (
    <UserStatsMonthlyChart
      title="월별 포인트 적립 현황"
      dateRange={dateRange}
      data={result}
    />
  );
};

const UserStatsDaily = async ({
  tenantCd,
  month,
}: {
  tenantCd: string;
  month: Date;
}) => {
  const result = await readEarnPointCountDaily(
    tenantCd,
    format(month, "yyyy-MM-dd"),
  );

  return (
    <UserStatsDailyChart
      title="일별 포인트 적립 현황"
      month={month}
      data={result}
    />
  );
};

export default async function PointsStatsPage({
  params,
  searchParams,
}: TenantPageProps & {
  searchParams: Promise<{ rangeFrom?: string; rangeTo?: string }>;
}) {
  const { tenantCd } = await params;
  const { rangeFrom, rangeTo } = await searchParams;
  const dateRange: [Date, Date] = [
    rangeFrom ? new Date(rangeFrom) : subYears(new Date(), 1),
    rangeTo ? new Date(rangeTo) : new Date(),
  ];
  return (
    <AppContent
      title="포인트 현황"
      titleSuffix={<PeriodPicker dateRange={dateRange} />}
    >
      <div className="flex flex-row gap-4">
        <div className="flex flex-1 flex-col gap-4">
          <Suspense fallback={<PointsStatsChartSkeleton />}>
            <PointsStatsMonthly tenantCd={tenantCd} dateRange={dateRange} />
          </Suspense>
          <Suspense fallback={<PointsStatsChartSkeleton />}>
            <UserStatsDaily tenantCd={tenantCd} month={dateRange[1]} />
          </Suspense>
        </div>
        <div>
          <PointsStatsOverview tenantCd={tenantCd} />
        </div>
      </div>
      <div className="flex flex-row gap-5">
        <ChartRadialShape />
        <ChartRadialShape />
        <ChartRadialShape />
        <ChartRadialShape />
        <ChartRadialShape />
      </div>
    </AppContent>
  );
}
